<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 尊享模式成员模型
 */

namespace App\Model\Copy;

use App\Model\Copy\Enums\ExpertType;
use App\Model\User\User;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\Database\Model\Relations\MorphTo;

/**
 * @property int $id 主键ID
 * @property int $invitation_id 邀请记录ID
 * @property int $expert_id 专家ID
 * @property string $expert_type 专家模型类名（多态关联）
 * @property int $expert_user_id 专家用户ID（冗余字段）
 * @property ExpertType $type 专家类型：1-合约，2-现货
 * @property int $follower_user_id 跟单者用户ID
 * @property string $profit_sharing_rate 分润比例（可单独修改）%
 * @property Carbon $joined_at 加入时间
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 * @property CopyExclusiveInvitation $invitation 邀请记录关联
 * @property CopyContractExpert|CopySpotExpert $expert 专家关联（多态）
 * @property User $followerUser 跟单者用户关联
 */
final class CopyExclusiveMember extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 邀请记录ID
     */
    public const FIELD_INVITATION_ID = 'invitation_id';
    /**
     * 专家ID
     */
    public const FIELD_EXPERT_ID = 'expert_id';
    /**
     * 专家模型类名（多态关联）
     */
    public const FIELD_EXPERT_TYPE = 'expert_type';
    /**
     * 专家用户ID（冗余字段）
     */
    public const FIELD_EXPERT_USER_ID = 'expert_user_id';
    /**
     * 专家类型：1-合约，2-现货
     */
    public const FIELD_TYPE = 'type';
    /**
     * 跟单者用户ID
     */
    public const FIELD_FOLLOWER_USER_ID = 'follower_user_id';
    /**
     * 分润比例（可单独修改）%
     */
    public const FIELD_PROFIT_SHARING_RATE = 'profit_sharing_rate';
    /**
     * 加入时间
     */
    public const FIELD_JOINED_AT = 'joined_at';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_exclusive_member';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'invitation_id', // 邀请记录ID
        'expert_id', // 专家ID
        'expert_type', // 专家模型类名（多态关联）
        'expert_user_id', // 专家用户ID（冗余字段）
        'type', // 专家类型：1-合约，2-现货
        'follower_user_id', // 跟单者用户ID
        'profit_sharing_rate', // 分润比例（可单独修改）%
        'joined_at', // 加入时间
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'invitation_id' => 'integer', // 邀请记录ID
        'expert_id' => 'integer', // 专家ID
        'expert_user_id' => 'integer', // 专家用户ID（冗余字段）
        'type' => ExpertType::class, // 专家类型：1-合约，2-现货
        'follower_user_id' => 'integer', // 跟单者用户ID
        'profit_sharing_rate' => 'decimal:4', // 分润比例（可单独修改）%
        'joined_at' => 'datetime', // 加入时间
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 邀请记录关联
     */
    public function invitation(): BelongsTo
    {
        return $this->belongsTo(CopyExclusiveInvitation::class, 'invitation_id', 'id');
    }

    /**
     * 专家关联（多态）
     */
    public function expert(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 跟单者用户关联
     */
    public function followerUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'follower_user_id', 'id');
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取邀请记录ID
     */
    public function getInvitationId(): int
    {
        return $this->invitation_id;
    }

    /**
     * 设置邀请记录ID
     */
    public function setInvitationId(int $value): static
    {
        $this->invitation_id = $value;
        return $this;
    }

    /**
     * 获取专家ID
     */
    public function getExpertId(): int
    {
        return $this->expert_id;
    }

    /**
     * 设置专家ID
     */
    public function setExpertId(int $value): static
    {
        $this->expert_id = $value;
        return $this;
    }

    /**
     * 获取专家模型类名（多态关联）
     */
    public function getExpertType(): string
    {
        return $this->expert_type;
    }

    /**
     * 设置专家模型类名（多态关联）
     */
    public function setExpertType(string $value): static
    {
        $this->expert_type = $value;
        return $this;
    }

    /**
     * 获取专家用户ID（冗余字段）
     */
    public function getExpertUserId(): int
    {
        return $this->expert_user_id;
    }

    /**
     * 设置专家用户ID（冗余字段）
     */
    public function setExpertUserId(int $value): static
    {
        $this->expert_user_id = $value;
        return $this;
    }

    /**
     * 获取专家类型
     */
    public function getType(): ExpertType
    {
        return $this->type;
    }

    /**
     * 设置专家类型
     */
    public function setType(ExpertType $value): static
    {
        $this->type = $value;
        return $this;
    }

    /**
     * 获取跟单者用户ID
     */
    public function getFollowerUserId(): int
    {
        return $this->follower_user_id;
    }

    /**
     * 设置跟单者用户ID
     */
    public function setFollowerUserId(int $value): static
    {
        $this->follower_user_id = $value;
        return $this;
    }

    /**
     * 获取分润比例（可单独修改）%
     */
    public function getProfitSharingRate(): string
    {
        return $this->profit_sharing_rate;
    }

    /**
     * 设置分润比例（可单独修改）%
     */
    public function setProfitSharingRate(string $value): static
    {
        $this->profit_sharing_rate = $value;
        return $this;
    }

    /**
     * 获取加入时间
     */
    public function getJoinedAt(): Carbon
    {
        return $this->joined_at;
    }

    /**
     * 设置加入时间
     */
    public function setJoinedAt(Carbon $value): static
    {
        $this->joined_at = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }
}
