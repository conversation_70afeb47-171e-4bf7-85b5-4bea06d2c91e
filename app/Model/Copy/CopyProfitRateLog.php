<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 分润比例修改记录模型
 */

namespace App\Model\Copy;

use App\Model\Copy\Enums\ExpertType;
use App\Model\User\User;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\Database\Model\Relations\MorphTo;

/**
 * @property int $id 主键ID
 * @property int $expert_id 专家ID
 * @property string $expert_type 专家模型类名（多态关联）
 * @property int $expert_user_id 专家用户ID（冗余字段）
 * @property ExpertType $type 专家类型：1-合约，2-现货
 * @property string|null $old_rate 原分润比例 %
 * @property string $new_rate 新分润比例 %
 * @property bool $is_exclusive 是否尊享模式
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 * @property CopyContractExpert|CopySpotExpert $expert 专家关联（多态）
 * @property User $expertUser 专家用户关联
 */
final class CopyProfitRateLog extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 专家ID
     */
    public const FIELD_EXPERT_ID = 'expert_id';
    /**
     * 专家模型类名（多态关联）
     */
    public const FIELD_EXPERT_TYPE = 'expert_type';
    /**
     * 专家用户ID（冗余字段）
     */
    public const FIELD_EXPERT_USER_ID = 'expert_user_id';
    /**
     * 专家类型：1-合约，2-现货
     */
    public const FIELD_TYPE = 'type';
    /**
     * 原分润比例 %
     */
    public const FIELD_OLD_RATE = 'old_rate';
    /**
     * 新分润比例 %
     */
    public const FIELD_NEW_RATE = 'new_rate';
    /**
     * 是否尊享模式
     */
    public const FIELD_IS_EXCLUSIVE = 'is_exclusive';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_profit_rate_log';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'expert_id', // 专家ID
        'expert_type', // 专家模型类名（多态关联）
        'expert_user_id', // 专家用户ID（冗余字段）
        'type', // 专家类型：1-合约，2-现货
        'old_rate', // 原分润比例 %
        'new_rate', // 新分润比例 %
        'is_exclusive', // 是否尊享模式
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'expert_id' => 'integer', // 专家ID
        'expert_user_id' => 'integer', // 专家用户ID（冗余字段）
        'type' => ExpertType::class, // 专家类型：1-合约，2-现货
        'old_rate' => 'decimal:4', // 原分润比例 %
        'new_rate' => 'decimal:4', // 新分润比例 %
        'is_exclusive' => 'boolean', // 是否尊享模式
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 专家关联（多态）
     */
    public function expert(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 专家用户关联
     */
    public function expertUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'expert_user_id', 'id');
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取专家ID
     */
    public function getExpertId(): int
    {
        return $this->expert_id;
    }

    /**
     * 设置专家ID
     */
    public function setExpertId(int $value): static
    {
        $this->expert_id = $value;
        return $this;
    }

    /**
     * 获取专家模型类名（多态关联）
     */
    public function getExpertType(): string
    {
        return $this->expert_type;
    }

    /**
     * 设置专家模型类名（多态关联）
     */
    public function setExpertType(string $value): static
    {
        $this->expert_type = $value;
        return $this;
    }

    /**
     * 获取专家用户ID（冗余字段）
     */
    public function getExpertUserId(): int
    {
        return $this->expert_user_id;
    }

    /**
     * 设置专家用户ID（冗余字段）
     */
    public function setExpertUserId(int $value): static
    {
        $this->expert_user_id = $value;
        return $this;
    }

    /**
     * 获取专家类型
     */
    public function getType(): ExpertType
    {
        return $this->type;
    }

    /**
     * 设置专家类型
     */
    public function setType(ExpertType $value): static
    {
        $this->type = $value;
        return $this;
    }

    /**
     * 获取原分润比例 %
     */
    public function getOldRate(): ?string
    {
        return $this->old_rate;
    }

    /**
     * 设置原分润比例 %
     */
    public function setOldRate(?string $value): static
    {
        $this->old_rate = $value;
        return $this;
    }

    /**
     * 获取新分润比例 %
     */
    public function getNewRate(): string
    {
        return $this->new_rate;
    }

    /**
     * 设置新分润比例 %
     */
    public function setNewRate(string $value): static
    {
        $this->new_rate = $value;
        return $this;
    }

    /**
     * 获取是否尊享模式
     */
    public function getIsExclusive(): bool
    {
        return $this->is_exclusive;
    }

    /**
     * 设置是否尊享模式
     */
    public function setIsExclusive(bool $value): static
    {
        $this->is_exclusive = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }
}
