<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_contract_order', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('follower_user_id')->unsigned()->comment('跟单者用户ID');
            $table->bigInteger('expert_id')->unsigned()->comment('专家ID');
            $table->bigInteger('expert_user_id')->unsigned()->comment('专家用户ID（冗余字段）');
            $table->tinyInteger('mode')->comment('跟单模式：1-智能比例，2-多元探索');
            $table->bigInteger('expert_order_id')->unsigned()->comment('专家订单ID（关联trade_perpetual_order）');
            $table->bigInteger('expert_position_id')->unsigned()->comment('专家仓位ID（关联trade_perpetual_position）');
            $table->bigInteger('follower_order_id')->unsigned()->comment('跟单者订单ID（关联trade_perpetual_order）');
            $table->bigInteger('follower_position_id')->unsigned()->comment('跟单者仓位ID（关联trade_perpetual_position）');
            $table->decimal('profit_sharing_rate', 8, 4)->comment('分润比例 %');
            $table->tinyInteger('is_exclusive')->default(0)->comment('是否尊享模式：1-是，0-否');
            $table->json('copy_settings_snapshot')->comment('跟单配置参数快照');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->useCurrent()->useCurrentOnUpdate()->comment('更新时间');

            // 索引
            $table->index('follower_user_id', 'idx_follower_user_id');
            $table->index('expert_id', 'idx_expert');
            $table->index('expert_user_id', 'idx_expert_user_id');
            $table->index(['expert_order_id', 'expert_position_id'], 'idx_expert_order_id');
            $table->index(['follower_order_id', 'follower_position_id'], 'idx_follower_order_id');

            $table->comment('合约跟单记录表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_contract_order');
    }
};
