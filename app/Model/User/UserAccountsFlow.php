<?php

declare(strict_types=1);

namespace App\Model\User;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $user_id 
 * @property int $account_id 账户id
 * @property int $currency_id 交易标的
 * @property int $type 资金变动类型 枚举类型：FlowsType
 * @property float $amount 变动金额
 * @property float $before 变动前余额
 * @property float $after 变动后余额
 * @property int $direction -1减 1 加
 * @property int $related_id 关联记录id
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class UserAccountsFlow extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 账户id
     */
    public const FIELD_ACCOUNT_ID = 'account_id';
    /**
     * 交易标的
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 资金变动类型 枚举类型：FlowsType
     */
    public const FIELD_TYPE = 'type';
    /**
     * 变动金额
     */
    public const FIELD_AMOUNT = 'amount';
    /**
     * 变动前余额
     */
    public const FIELD_BEFORE = 'before';
    /**
     * 变动后余额
     */
    public const FIELD_AFTER = 'after';
    /**
     * -1减 1 加
     */
    public const FIELD_DIRECTION = 'direction';
    /**
     * 关联记录id
     */
    public const FIELD_RELATED_ID = 'related_id';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'user_accounts_flows';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'account_id', 'currency_id', 'type', 'amount', 'before', 'after', 'direction', 'related_id', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer', 'user_id' => 'integer', 'account_id' => 'integer', 'currency_id' => 'integer', 'type' => 'integer', 'direction' => 'integer', 'related_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getAccountId() : int
    {
        return $this->account_id;
    }
    public function setAccountId($value) : object
    {
        $this->account_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getType() : int
    {
        return $this->type;
    }
    public function setType($value) : object
    {
        $this->type = $value;
        return $this;
    }
    public function getAmount() : float
    {
        return (float)$this->amount;
    }
    public function setAmount($value) : object
    {
        $this->amount = $value;
        return $this;
    }
    public function getBefore() : float
    {
        return (float)$this->before;
    }
    public function setBefore($value) : object
    {
        $this->before = $value;
        return $this;
    }
    public function getAfter() : float
    {
        return (float)$this->after;
    }
    public function setAfter($value) : object
    {
        $this->after = $value;
        return $this;
    }
    public function getDirection() : int
    {
        return $this->direction;
    }
    public function setDirection($value) : object
    {
        $this->direction = $value;
        return $this;
    }
    public function getRelatedId() : int
    {
        return $this->related_id;
    }
    public function setRelatedId($value) : object
    {
        $this->related_id = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
