<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_spot_expert', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('user_id')->unsigned()->comment('用户ID');
            $table->text('introduction')->comment('个人介绍');
            $table->tinyInteger('status')->default(1)->comment('申请状态：1-待审核，2-审核通过，3-审核拒绝');
            $table->string('review_remark', 500)->nullable()->comment('审核备注');
            $table->timestamp('reviewed_at')->nullable()->comment('审核时间');
            $table->bigInteger('reviewed_by')->unsigned()->nullable()->comment('审核人ID');
            $table->tinyInteger('is_active')->default(0)->comment('是否开启现货带单：1-是，0-否');
            $table->tinyInteger('show_total_assets')->default(0)->comment('是否展示总资产：1-是，0-否');
            $table->tinyInteger('show_fund_composition')->default(0)->comment('是否展示资金构成：1-是，0-否');
            $table->tinyInteger('new_currency_auto_copy')->default(0)->comment('新上线的交易对自动开启带单：1-是，0-否');
            $table->tinyInteger('position_protection')->default(1)->comment('未结仓位保护：1-开启，0-关闭');
            $table->decimal('min_follow_amount', 20, 8)->nullable()->comment('最小跟单金额（USDT）');
            $table->json('recommend_params')->nullable()->comment('推荐参数配置');
            $table->json('currency_ids')->nullable()->comment('跟单币种 id 配置（支持多币种）');
            $table->decimal('profit_sharing_rate', 8, 4)->default(0.0000)->comment('分润比例 %');
            $table->bigInteger('level_id')->unsigned()->default(1)->comment('专家等级ID');
            $table->tinyInteger('exclusive_mode')->default(0)->comment('尊享模式：1-开启，0-关闭');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->useCurrent()->useCurrentOnUpdate()->comment('更新时间');

            // 索引
            $table->unique('user_id', 'uk_user_id');
            $table->index('status', 'idx_status');
            $table->index('is_active', 'idx_is_active');
            $table->index('level_id', 'idx_level_id');
            $table->index('exclusive_mode', 'idx_exclusive_mode');

            $table->comment('现货交易专家表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_spot_expert');
    }
};
