<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_expert_level', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->tinyInteger('type')->comment('类型：1-合约，2-现货');
            $table->integer('level')->comment('等级');
            $table->string('name', 50)->comment('等级名称');
            $table->string('icon', 255)->nullable()->comment('等级图标');
            $table->decimal('condition_amount', 20, 8)->comment('条件一：带单金额（USDT）');
            $table->decimal('condition_follow_amount', 20, 8)->nullable()->comment('条件二：跟单者总跟单资金（USDT）');
            $table->integer('condition_follow_count')->nullable()->comment('条件二：跟单交易人数（人）');
            $table->integer('max_follow_count')->comment('最大可带单人数');
            $table->decimal('max_profit_rate', 8, 4)->comment('最大分润比例 %');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->useCurrent()->useCurrentOnUpdate()->comment('更新时间');

            // 索引
            $table->unique(['type', 'level'], 'uk_type_level');

            $table->comment('交易专家等级表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_expert_level');
    }
};
