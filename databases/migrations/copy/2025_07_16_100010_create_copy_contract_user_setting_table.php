<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_contract_user_setting', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('follower_user_id')->unsigned()->comment('跟单者用户ID');
            $table->bigInteger('expert_id')->unsigned()->comment('专家ID');
            $table->bigInteger('expert_user_id')->unsigned()->comment('专家用户ID（冗余字段）');
            $table->tinyInteger('mode')->comment('跟单模式：1-智能比例，2-多元探索');
            $table->decimal('investment_amount', 20, 8)->nullable()->comment('投资金额（USDT）（智能比例模式）');
            $table->tinyInteger('copy_type')->nullable()->comment('跟单方式：1-固定额度，2-倍率（多元探索模式）');
            $table->decimal('fixed_amount', 20, 8)->nullable()->comment('固定额度（USDT）（多元探索模式）');
            $table->decimal('rate', 8, 4)->nullable()->comment('倍率 %（多元探索模式）');
            $table->decimal('stop_loss_rate', 8, 4)->nullable()->comment('止损比例 %');
            $table->decimal('take_profit_rate', 8, 4)->nullable()->comment('止盈比例 %');
            $table->decimal('max_follow_amount', 20, 8)->nullable()->comment('最大跟随金额');
            $table->decimal('slippage_rate', 8, 4)->nullable()->comment('滑点比例 %');
            $table->tinyInteger('auto_new_pairs')->default(0)->comment('自动跟随新币对：1-是，0-否');
            $table->tinyInteger('is_exclusive')->default(0)->comment('是否尊享模式：1-是，0-否');
            $table->json('copy_currencies')->nullable()->comment('跟单币种配置（支持多币种）');
            $table->tinyInteger('net_value_guardian')->default(0)->comment('净值守护者：1-开启，0-关闭（智能比例模式）');
            $table->decimal('max_loss_amount', 20, 8)->nullable()->comment('最大亏损金额（触发则解除跟单）（智能比例模式）');
            $table->decimal('min_net_value', 20, 8)->nullable()->comment('最小净值金额（触发则解除跟单）（智能比例模式）');
            $table->tinyInteger('copy_all_positions')->default(0)->comment('跟单后是否复制全部仓位：1-是，0-否（智能比例模式）');
            $table->tinyInteger('margin_mode')->default(1)->comment('保证金模式：1-跟随专家，2-全仓，3-逐仓');
            $table->tinyInteger('leverage_mode')->default(1)->comment('杠杆设置：1-跟随专家，2-指定杠杆');
            $table->integer('custom_leverage')->nullable()->comment('自定义杠杆倍数（仅当leverage_mode=2时有效）');
            $table->tinyInteger('status')->default(1)->comment('状态：1-跟单中，2-暂停');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->useCurrent()->useCurrentOnUpdate()->comment('更新时间');

            // 索引
            $table->unique(['follower_user_id', 'expert_id'], 'uk_follower_expert');
            $table->index('expert_id', 'idx_expert');
            $table->index('expert_user_id', 'idx_expert_user_id');
            $table->index('status', 'idx_status');

            $table->comment('用户跟单配置表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_contract_user_setting');
    }
};
