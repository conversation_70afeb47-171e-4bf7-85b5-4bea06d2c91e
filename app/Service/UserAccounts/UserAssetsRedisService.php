<?php

declare(strict_types=1);

namespace App\Service\UserAccounts;

use App\Enum\User\UserAssetsCacheKey;
use App\Enum\UserAssets\UserAssetsFlowsCacheKey;
use App\Service\RedisFactory\CacheRedis;
use Carbon\Carbon;
use Hyperf\Context\ApplicationContext;
use Psr\Log\LoggerInterface;

/**
 * 用户资产Redis操作服务类
 * 专门处理Redis资金操作，使用Lua脚本保证原子性
 */
class UserAssetsRedisService
{
    use UserAssetsTrait;

    protected CacheRedis $redis;
    protected LoggerInterface $logger;

    public const DEFAULT_PRECISION = 8;
    public const BALANCE_TOLERANCE = '0.********';

    // Lua脚本SHA缓存
    private static array $scriptShaCache = [];

    public function __construct()
    {
        $this->redis = ApplicationContext::getContainer()->get(CacheRedis::class);
        $this->logger = logger('userAccounts', 'userAccounts/redis-assets.log');
        $this->initLuaScripts();
    }

    /**
     * 初始化Lua脚本SHA缓存
     */
    private function initLuaScripts(): void
    {
        if (!empty(self::$scriptShaCache)) {
            return;
        }

        $scripts = [
            'asset_freeze' => $this->getScriptContent('asset_freeze'),
            'asset_unfreeze' => $this->getScriptContent('asset_unfreeze'),
            'asset_deduct' => $this->getScriptContent('asset_deduct'),
            'asset_add' => $this->getScriptContent('asset_add'),
            'get_user_assets' => $this->getScriptContent('get_user_assets'),
        ];

        foreach ($scripts as $name => $script) {
            try {
                $sha = $this->redis->script('LOAD', $script);
                self::$scriptShaCache[$name] = $sha;
            } catch (\Throwable $e) {
                $this->logger->error("加载Lua脚本失败: {$name}", ['error' => $e->getMessage()]);
                throw new \RuntimeException("加载Lua脚本失败: {$name}");
            }
        }
    }

    /**
     * 获取脚本内容
     */
    private function getScriptContent(string $scriptName): string
    {
        $scriptPath = BASE_PATH . '/storage/lua/' . $scriptName . '.lua';
        if (!file_exists($scriptPath)) {
            throw new \RuntimeException("Lua脚本不存在: {$scriptPath}");
        }
        return file_get_contents($scriptPath);
    }

    /**
     * 执行Lua脚本
     */
    private function evalScript(string $scriptName, array $keys, array $argv): array
    {
        $sha = self::$scriptShaCache[$scriptName] ?? null;
        if (!$sha) {
            throw new \RuntimeException("Lua脚本未加载: {$scriptName}");
        }

        try {
            return $this->redis->evalSha($sha, array_merge($keys, $argv), count($keys));
        } catch (\Throwable $e) {
            if (strpos($e->getMessage(), 'NOSCRIPT') !== false) {
                $this->logger->warning("Lua脚本需要重新加载: {$scriptName}");
                $script = $this->getScriptContent($scriptName);
                $sha = $this->redis->script('LOAD', $script);
                self::$scriptShaCache[$scriptName] = $sha;
                return $this->redis->evalSha($sha, array_merge($keys, $argv), count($keys));
            }
            throw $e;
        }
    }

    /**
     * 冻结用户资金
     */
    public function freezeAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'available',
        string $frozenField = 'frozen'
    ): bool {
        if (bccomp((string)$amount, '0', self::DEFAULT_PRECISION) <= 0) {
            throw new \InvalidArgumentException('冻结金额必须大于0');
        }

        $assetKey = UserAssetsCacheKey::getAssetKey($userId, $accountType, $currencyId);
        $result = $this->evalScript('asset_freeze', [$assetKey], [(string)$amount, $assetField, $frozenField]);

        if (!$result[0]) {
            $this->logger->error('冻结资金失败', [
                'user_id' => $userId,
                'account_type' => $accountType,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'error' => $result[1],
            ]);
            throw new \RuntimeException('冻结资金失败: ' . $result[1]);
        }

        // 记录流水
        $this->createFlow($userId, 0, $currencyId, $flowType, $amount, (float)$result[1], (float)$result[2], -1, $relatedId);
        $this->createFlow($userId, 0, $currencyId, $flowType, $amount, (float)$result[3], (float)$result[4], 1, $relatedId);

        // 维护索引和触发缓存更新
        $this->maintainAssetIndex($userId, $accountType, $currencyId);

        // 同步数据库操作
        $this->syncDbAssets([
            'callback' => 'freezeAssetWithMysql',
            'userId' => $userId,
            'accountType' => $accountType,
            'currencyId' => $currencyId,
            'amount' => $amount,
            'flowType' => $flowType,
            'relatedId' => $relatedId,
            'assetField' => $assetField,
            'frozenField' => $frozenField,
            'createFlow' => false
        ]);

        return true;
    }

    /**
     * 解冻用户资金
     */
    public function unfreezeAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $frozenField = 'frozen',
        string $targetField = 'available'
    ): bool {
        if (bccomp((string)$amount, '0', self::DEFAULT_PRECISION) <= 0) {
            throw new \InvalidArgumentException('解冻金额必须大于0');
        }

        $assetKey = UserAssetsCacheKey::getAssetKey($userId, $accountType, $currencyId);
        $result = $this->evalScript('asset_unfreeze', [$assetKey], [(string)$amount, $frozenField, $targetField]);

        if (!$result[0]) {
            $this->logger->error('解冻资金失败', [
                'user_id' => $userId,
                'account_type' => $accountType,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'error' => $result[1],
            ]);
            throw new \RuntimeException('解冻资金失败: ' . $result[1]);
        }

        // 记录流水
        $this->createFlow($userId, 0, $currencyId, $flowType, $amount, (float)$result[1], (float)$result[2], -1, $relatedId);
        $this->createFlow($userId, 0, $currencyId, $flowType, $amount, (float)$result[3], (float)$result[4], 1, $relatedId);

        // 维护索引和触发缓存更新
        $this->maintainAssetIndex($userId, $accountType, $currencyId);

        // 同步数据库操作
        $this->syncDbAssets([
            'callback' => 'unfreezeAssetWithMysql',
            'userId' => $userId,
            'accountType' => $accountType,
            'currencyId' => $currencyId,
            'amount' => $amount,
            'flowType' => $flowType,
            'relatedId' => $relatedId,
            'frozenField' => $frozenField,
            'targetField' => $targetField,
            'createFlow' => false
        ]);

        return true;
    }

    /**
     * 扣减用户资金
     */
    public function deductAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'available'
    ): bool {
        if (bccomp((string)$amount, '0', self::DEFAULT_PRECISION) <= 0) {
            throw new \InvalidArgumentException('扣减金额必须大于0');
        }

        $assetKey = UserAssetsCacheKey::getAssetKey($userId, $accountType, $currencyId);
        $result = $this->evalScript('asset_deduct', [$assetKey], [(string)$amount, $assetField]);

        if (!$result[0]) {
            $this->logger->error('扣减资金失败', [
                'user_id' => $userId,
                'account_type' => $accountType,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'error' => $result[1],
            ]);
            throw new \RuntimeException('扣减资金失败: ' . $result[1]);
        }

        // 记录流水
        $this->createFlow($userId, 0, $currencyId, $flowType, $amount, (float)$result[1], (float)$result[2], -1, $relatedId);

        // 维护索引和触发缓存更新
        $this->maintainAssetIndex($userId, $accountType, $currencyId);

        // 同步数据库操作
        $this->syncDbAssets([
            'callback' => 'deductAvailableAssetWithMysql',
            'userId' => $userId,
            'accountType' => $accountType,
            'currencyId' => $currencyId,
            'amount' => $amount,
            'flowType' => $flowType,
            'relatedId' => $relatedId,
            'assetField' => $assetField,
            'createFlow' => false
        ]);

        return true;
    }

    /**
     * 增加用户资金
     */
    public function addAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'available'
    ): bool {
        if (bccomp((string)$amount, '0', self::DEFAULT_PRECISION) <= 0) {
            throw new \InvalidArgumentException('增加金额必须大于0');
        }

        $assetKey = UserAssetsCacheKey::getAssetKey($userId, $accountType, $currencyId);
        $result = $this->evalScript('asset_add', [$assetKey], [(string)$amount, $assetField]);

        if (!$result[0]) {
            $this->logger->error('增加资金失败', [
                'user_id' => $userId,
                'account_type' => $accountType,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'error' => $result[1],
            ]);
            throw new \RuntimeException('增加资金失败: ' . $result[1]);
        }

        // 记录流水
        $this->createFlow($userId, 0, $currencyId, $flowType, $amount, (float)$result[1], (float)$result[2], 1, $relatedId);

        // 维护索引和触发缓存更新
        $this->maintainAssetIndex($userId, $accountType, $currencyId);

        // 同步数据库操作
        $this->syncDbAssets([
            'callback' => 'addAvailableAssetWithMysql',
            'userId' => $userId,
            'accountType' => $accountType,
            'currencyId' => $currencyId,
            'amount' => $amount,
            'flowType' => $flowType,
            'relatedId' => $relatedId,
            'assetField' => $assetField,
            'createFlow' => false
        ]);

        return true;
    }

    /**
     * 获取用户资产
     */
    public function getUserAsset(int $userId, int $accountType, int $currencyId): ?array
    {
        $assetKey = UserAssetsCacheKey::getAssetKey($userId, $accountType, $currencyId);
        $data = $this->redis->hGetAll($assetKey);

        if (empty($data)) {
            return null;
        }

        return [
            'user_id' => $userId,
            'account_type' => $accountType,
            'currency_id' => $currencyId,
            'available' => (float)($data['available'] ?? 0),
            'frozen' => (float)($data['frozen'] ?? 0),
            'locked' => (float)($data['locked'] ?? 0),
            'margin_quote' => (float)($data['margin_quote'] ?? 0),
            'borrowed_amount' => (float)($data['borrowed_amount'] ?? 0),
            'interest_amount' => (float)($data['interest_amount'] ?? 0),
        ];
    }

    /**
     * 批量获取用户资产
     */
    public function getUserAssets(int $userId, ?int $accountType = null): array
    {
        $indexKey = UserAssetsCacheKey::getUserAssetsIndexKey($userId);
        $accountTypeFilter = $accountType ?? 0;
        $onlyWithBalance = $accountType !== null ? 1 : 0;

        $result = $this->evalScript('get_user_assets', [$indexKey], [$accountTypeFilter, $onlyWithBalance]);

        $assets = [];
        foreach ($result as $assetData) {
            $assets[] = [
                'user_id' => $userId,
                'account_type' => (int)$assetData['account_type'],
                'currency_id' => (int)$assetData['currency_id'],
                'available' => (float)$assetData['available'],
                'frozen' => (float)$assetData['frozen'],
                'locked' => (float)$assetData['locked'],
                'margin_quote' => (float)$assetData['margin_quote'],
                'borrowed_amount' => (float)$assetData['borrowed_amount'],
                'interest_amount' => (float)$assetData['interest_amount'],
            ];
        }

        return $assets;
    }

    /**
     * 维护用户资产索引
     */
    private function maintainAssetIndex(int $userId, int $accountType, int $currencyId): void
    {
        $indexKey = UserAssetsCacheKey::getUserAssetsIndexKey($userId);
        $indexValue = "{$accountType}:{$currencyId}";
        $this->redis->sAdd($indexKey, $indexValue);
    }

    /**
     * 增加借款金额
     */
    public function addBorrowedAmount(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0
    ): bool {
        if (bccomp((string)$amount, '0', self::DEFAULT_PRECISION) <= 0) {
            throw new \InvalidArgumentException('借款金额必须大于0');
        }

        $assetKey = UserAssetsCacheKey::getAssetKey($userId, $accountType, $currencyId);

        // 增加borrowed_amount字段
        $borrowResult = $this->evalScript('asset_add', [$assetKey], [(string)$amount, 'borrowed_amount']);
        if (!$borrowResult[0]) {
            throw new \RuntimeException('增加借款金额失败: ' . $borrowResult[1]);
        }

        // 增加available字段
        $availableResult = $this->evalScript('asset_add', [$assetKey], [(string)$amount, 'available']);
        if (!$availableResult[0]) {
            throw new \RuntimeException('增加可用余额失败: ' . $availableResult[1]);
        }

        // 记录流水
        $this->createFlow($userId, 0, $currencyId, $flowType, $amount, (float)$borrowResult[1], (float)$borrowResult[2], 1, $relatedId);
        $this->createFlow($userId, 0, $currencyId, $flowType, $amount, (float)$availableResult[1], (float)$availableResult[2], 1, $relatedId);

        $this->maintainAssetIndex($userId, $accountType, $currencyId);

        // 同步数据库操作
        $this->syncDbAssets([
            'callback' => 'addBorrowedAmountWithMysql',
            'userId' => $userId,
            'accountType' => $accountType,
            'currencyId' => $currencyId,
            'amount' => $amount,
            'flowType' => $flowType,
            'relatedId' => $relatedId,
            'assetField' => 'borrowed_amount',
            'availableField' => 'available',
            'createFlow' => false
        ]);

        return true;
    }

    /**
     * 减少借款金额（还款）
     */
    public function reduceBorrowedAmount(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0
    ): bool {
        if (bccomp((string)$amount, '0', self::DEFAULT_PRECISION) <= 0) {
            throw new \InvalidArgumentException('还款金额必须大于0');
        }

        $assetKey = UserAssetsCacheKey::getAssetKey($userId, $accountType, $currencyId);

        // 扣减available字段
        $availableResult = $this->evalScript('asset_deduct', [$assetKey], [(string)$amount, 'available']);
        if (!$availableResult[0]) {
            throw new \RuntimeException('扣减可用余额失败: ' . $availableResult[1]);
        }

        // 扣减borrowed_amount字段
        $borrowResult = $this->evalScript('asset_deduct', [$assetKey], [(string)$amount, 'borrowed_amount']);
        if (!$borrowResult[0]) {
            throw new \RuntimeException('减少借款金额失败: ' . $borrowResult[1]);
        }

        // 记录流水
        $this->createFlow($userId, 0, $currencyId, $flowType, $amount, (float)$availableResult[1], (float)$availableResult[2], -1, $relatedId);
        $this->createFlow($userId, 0, $currencyId, $flowType, $amount, (float)$borrowResult[1], (float)$borrowResult[2], -1, $relatedId);

        $this->maintainAssetIndex($userId, $accountType, $currencyId);

        // 同步数据库操作
        $this->syncDbAssets([
            'callback' => 'reduceBorrowedAmountWithMysql',
            'userId' => $userId,
            'accountType' => $accountType,
            'currencyId' => $currencyId,
            'amount' => $amount,
            'flowType' => $flowType,
            'relatedId' => $relatedId,
            'createFlow' => false
        ]);

        return true;
    }

    /**
     * 增加利息金额
     */
    public function addInterestAmount(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0
    ): bool {
        if (bccomp((string)$amount, '0', self::DEFAULT_PRECISION) <= 0) {
            throw new \InvalidArgumentException('利息金额必须大于0');
        }

        $assetKey = UserAssetsCacheKey::getAssetKey($userId, $accountType, $currencyId);
        $result = $this->evalScript('asset_add', [$assetKey], [(string)$amount, 'interest_amount']);

        if (!$result[0]) {
            throw new \RuntimeException('增加利息金额失败: ' . $result[1]);
        }

        // 记录流水
        $this->createFlow($userId, 0, $currencyId, $flowType, $amount, (float)$result[1], (float)$result[2], 1, $relatedId);

        $this->maintainAssetIndex($userId, $accountType, $currencyId);

        // 同步数据库操作
        $this->syncDbAssets([
            'callback' => 'addInterestAmountWithMysql',
            'userId' => $userId,
            'accountType' => $accountType,
            'currencyId' => $currencyId,
            'amount' => $amount,
            'flowType' => $flowType,
            'relatedId' => $relatedId,
            'createFlow' => false
        ]);

        return true;
    }

    /**
     * 检查余额是否足够
     */
    public function checkBalance(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        string $field = 'available'
    ): bool {
        $asset = $this->getUserAsset($userId, $accountType, $currencyId);
        if (!$asset) {
            return false;
        }

        return bccomp((string)($asset[$field] ?? 0), (string)$amount, self::DEFAULT_PRECISION) >= 0;
    }
}
