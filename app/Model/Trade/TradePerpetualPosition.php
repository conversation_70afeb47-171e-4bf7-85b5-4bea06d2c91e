<?php

declare(strict_types=1);

namespace App\Model\Trade;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 仓位ID
 * @property int $user_id 用户ID
 * @property int $currency_id 币种ID
 * @property int $margin_mode 保证金模式：1-全仓，2-逐仓
 * @property int $side 方向：1-多头，2-空头
 * @property float $quantity 持仓数量（币本位）
 * @property float $available_quantity 可用数量（可平仓数量）
 * @property float $frozen_quantity 冻结数量（挂单占用）
 * @property float $entry_price 开仓均价
 * @property float $margin_amount 保证金金额
 * @property float $initial_margin 初始保证金
 * @property float $maintenance_margin 维持保证金
 * @property float $realized_pnl 已实现盈亏
 * @property float $leverage 杠杆倍数
 * @property int $auto_add_margin 自动追加保证金（仅逐仓）
 * @property float $total_charge 总手续费
 * @property float $total_funding 总资金费用
 * @property int $status 状态：1-持仓中，2-已平仓，3-强制平仓
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class TradePerpetualPosition extends Model
{
    /**
     * 仓位ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 保证金模式：1-全仓，2-逐仓
     */
    public const FIELD_MARGIN_MODE = 'margin_mode';
    /**
     * 方向：1-多头，2-空头
     */
    public const FIELD_SIDE = 'side';
    /**
     * 持仓数量（币本位）
     */
    public const FIELD_QUANTITY = 'quantity';
    /**
     * 可用数量（可平仓数量）
     */
    public const FIELD_AVAILABLE_QUANTITY = 'available_quantity';
    /**
     * 冻结数量（挂单占用）
     */
    public const FIELD_FROZEN_QUANTITY = 'frozen_quantity';
    /**
     * 开仓均价
     */
    public const FIELD_ENTRY_PRICE = 'entry_price';
    /**
     * 保证金金额
     */
    public const FIELD_MARGIN_AMOUNT = 'margin_amount';
    /**
     * 初始保证金
     */
    public const FIELD_INITIAL_MARGIN = 'initial_margin';
    /**
     * 维持保证金
     */
    public const FIELD_MAINTENANCE_MARGIN = 'maintenance_margin';
    /**
     * 已实现盈亏
     */
    public const FIELD_REALIZED_PNL = 'realized_pnl';
    /**
     * 杠杆倍数
     */
    public const FIELD_LEVERAGE = 'leverage';
    /**
     * 自动追加保证金（仅逐仓）
     */
    public const FIELD_AUTO_ADD_MARGIN = 'auto_add_margin';

    public const  FIELD_TOTAL_CHARGE = 'total_charge';

    public const FIELD_TOTAL_FUNDING = 'total_funding';
    /**
     * 状态：1-持仓中，2-已平仓，3-强制平仓
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_perpetual_position';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'currency_id', 'margin_mode', 'side', 'quantity', 'available_quantity', 'frozen_quantity', 'entry_price', 'margin_amount', 'initial_margin', 'maintenance_margin', 'realized_pnl', 'leverage', 'auto_add_margin','total_charge','total_funding', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        // 仓位ID
        'id' => 'integer',
        // 用户ID
        'user_id' => 'integer',
        // 币种ID
        'currency_id' => 'integer',
        // 保证金模式：1-全仓，2-逐仓
        'margin_mode' => 'integer',
        // 方向：1-多头，2-空头
        'side' => 'integer',
        // 持仓数量（币本位）
        'quantity' => 'float',
        // 可用数量（可平仓数量）
        'available_quantity' => 'float',
        // 冻结数量（挂单占用）
        'frozen_quantity' => 'float',
        // 开仓均价
        'entry_price' => 'float',
        // 保证金金额
        'margin_amount' => 'float',
        // 初始保证金
        'initial_margin' => 'float',
        // 维持保证金
        'maintenance_margin' => 'float',
        // 已实现盈亏
        'realized_pnl' => 'float',
        // 杠杆倍数
        'leverage' => 'float',
        // 自动追加保证金（仅逐仓）
        'auto_add_margin' => 'integer',
        'total_charge' => 'float',
        'total_funding' => 'float',
        // 状态：1-持仓中，2-已平仓，3-强制平仓
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getMarginMode() : int
    {
        return $this->margin_mode;
    }
    public function setMarginMode($value) : object
    {
        $this->margin_mode = $value;
        return $this;
    }
    public function getSide() : int
    {
        return $this->side;
    }
    public function setSide($value) : object
    {
        $this->side = $value;
        return $this;
    }
    public function getQuantity() : float
    {
        return $this->quantity;
    }
    public function setQuantity($value) : object
    {
        $this->quantity = $value;
        return $this;
    }
    public function getAvailableQuantity() : float
    {
        return $this->available_quantity;
    }
    public function setAvailableQuantity($value) : object
    {
        $this->available_quantity = $value;
        return $this;
    }
    public function getFrozenQuantity() : float
    {
        return $this->frozen_quantity;
    }
    public function setFrozenQuantity($value) : object
    {
        $this->frozen_quantity = $value;
        return $this;
    }
    public function getEntryPrice() : float
    {
        return $this->entry_price;
    }
    public function setEntryPrice($value) : object
    {
        $this->entry_price = $value;
        return $this;
    }
    public function getMarginAmount() : float
    {
        return $this->margin_amount;
    }
    public function setMarginAmount($value) : object
    {
        $this->margin_amount = $value;
        return $this;
    }
    public function getInitialMargin() : float
    {
        return $this->initial_margin;
    }
    public function setInitialMargin($value) : object
    {
        $this->initial_margin = $value;
        return $this;
    }
    public function getMaintenanceMargin() : float
    {
        return $this->maintenance_margin;
    }
    public function setMaintenanceMargin($value) : object
    {
        $this->maintenance_margin = $value;
        return $this;
    }
    public function getRealizedPnl() : float
    {
        return $this->realized_pnl;
    }
    public function setRealizedPnl($value) : object
    {
        $this->realized_pnl = $value;
        return $this;
    }
    public function getLeverage() : float
    {
        return $this->leverage;
    }
    public function setLeverage($value) : object
    {
        $this->leverage = $value;
        return $this;
    }
    public function getAutoAddMargin() : int
    {
        return $this->auto_add_margin;
    }
    public function setAutoAddMargin($value) : object
    {
        $this->auto_add_margin = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
