<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 尊享模式邀请模型
 */

namespace App\Model\Copy;

use App\Model\Copy\Enums\ExpertType;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\HasMany;
use Hyperf\Database\Model\Relations\MorphTo;

/**
 * @property int $id 主键ID
 * @property int $expert_id 专家ID
 * @property string $expert_type 专家模型类名（多态关联）
 * @property int $expert_user_id 专家用户ID（冗余字段）
 * @property ExpertType $type 专家类型：1-合约，2-现货
 * @property string|null $title 链接标题
 * @property string $invite_code 邀请码
 * @property int|null $max_count 最大邀请人数
 * @property int $current_count 当前邀请人数
 * @property string|null $profit_sharing_rate 分润比例 %
 * @property Carbon|null $expired_at 过期时间
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 * @property CopyContractExpert|CopySpotExpert $expert 专家关联（多态）
 */
final class CopyExclusiveInvitation extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 专家ID
     */
    public const FIELD_EXPERT_ID = 'expert_id';
    /**
     * 专家模型类名（多态关联）
     */
    public const FIELD_EXPERT_TYPE = 'expert_type';
    /**
     * 专家用户ID（冗余字段）
     */
    public const FIELD_EXPERT_USER_ID = 'expert_user_id';
    /**
     * 专家类型：1-合约，2-现货
     */
    public const FIELD_TYPE = 'type';
    /**
     * 链接标题
     */
    public const FIELD_TITLE = 'title';
    /**
     * 邀请码
     */
    public const FIELD_INVITE_CODE = 'invite_code';
    /**
     * 最大邀请人数
     */
    public const FIELD_MAX_COUNT = 'max_count';
    /**
     * 当前邀请人数
     */
    public const FIELD_CURRENT_COUNT = 'current_count';
    /**
     * 分润比例 %
     */
    public const FIELD_PROFIT_SHARING_RATE = 'profit_sharing_rate';
    /**
     * 过期时间
     */
    public const FIELD_EXPIRED_AT = 'expired_at';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_exclusive_invitation';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'expert_id', // 专家ID
        'expert_type', // 专家模型类名（多态关联）
        'expert_user_id', // 专家用户ID（冗余字段）
        'type', // 专家类型：1-合约，2-现货
        'title', // 链接标题
        'invite_code', // 邀请码
        'max_count', // 最大邀请人数
        'current_count', // 当前邀请人数
        'profit_sharing_rate', // 分润比例 %
        'expired_at', // 过期时间
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'expert_id' => 'integer', // 专家ID
        'expert_user_id' => 'integer', // 专家用户ID（冗余字段）
        'type' => ExpertType::class, // 专家类型：1-合约，2-现货
        'max_count' => 'integer', // 最大邀请人数
        'current_count' => 'integer', // 当前邀请人数
        'profit_sharing_rate' => 'decimal:4', // 分润比例 %
        'expired_at' => 'datetime', // 过期时间
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 专家关联（多态）
     */
    public function expert(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 尊享模式成员关联
     */
    public function members(): HasMany
    {
        return $this->hasMany(CopyExclusiveMember::class, 'invitation_id', 'id');
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取专家ID
     */
    public function getExpertId(): int
    {
        return $this->expert_id;
    }

    /**
     * 设置专家ID
     */
    public function setExpertId(int $value): static
    {
        $this->expert_id = $value;
        return $this;
    }

    /**
     * 获取专家模型类名（多态关联）
     */
    public function getExpertType(): string
    {
        return $this->expert_type;
    }

    /**
     * 设置专家模型类名（多态关联）
     */
    public function setExpertType(string $value): static
    {
        $this->expert_type = $value;
        return $this;
    }

    /**
     * 获取专家用户ID（冗余字段）
     */
    public function getExpertUserId(): int
    {
        return $this->expert_user_id;
    }

    /**
     * 设置专家用户ID（冗余字段）
     */
    public function setExpertUserId(int $value): static
    {
        $this->expert_user_id = $value;
        return $this;
    }

    /**
     * 获取专家类型
     */
    public function getType(): ExpertType
    {
        return $this->type;
    }

    /**
     * 设置专家类型
     */
    public function setType(ExpertType $value): static
    {
        $this->type = $value;
        return $this;
    }

    /**
     * 获取链接标题
     */
    public function getTitle(): ?string
    {
        return $this->title;
    }

    /**
     * 设置链接标题
     */
    public function setTitle(?string $value): static
    {
        $this->title = $value;
        return $this;
    }

    /**
     * 获取邀请码
     */
    public function getInviteCode(): string
    {
        return $this->invite_code;
    }

    /**
     * 设置邀请码
     */
    public function setInviteCode(string $value): static
    {
        $this->invite_code = $value;
        return $this;
    }

    /**
     * 获取最大邀请人数
     */
    public function getMaxCount(): ?int
    {
        return $this->max_count;
    }

    /**
     * 设置最大邀请人数
     */
    public function setMaxCount(?int $value): static
    {
        $this->max_count = $value;
        return $this;
    }

    /**
     * 获取当前邀请人数
     */
    public function getCurrentCount(): int
    {
        return $this->current_count;
    }

    /**
     * 设置当前邀请人数
     */
    public function setCurrentCount(int $value): static
    {
        $this->current_count = $value;
        return $this;
    }

    /**
     * 获取分润比例 %
     */
    public function getProfitSharingRate(): ?string
    {
        return $this->profit_sharing_rate;
    }

    /**
     * 设置分润比例 %
     */
    public function setProfitSharingRate(?string $value): static
    {
        $this->profit_sharing_rate = $value;
        return $this;
    }

    /**
     * 获取过期时间
     */
    public function getExpiredAt(): ?Carbon
    {
        return $this->expired_at;
    }

    /**
     * 设置过期时间
     */
    public function setExpiredAt(?Carbon $value): static
    {
        $this->expired_at = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }
}
