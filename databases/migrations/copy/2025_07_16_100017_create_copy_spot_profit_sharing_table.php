<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_spot_profit_sharing', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('expert_id')->unsigned()->comment('专家ID');
            $table->bigInteger('expert_user_id')->unsigned()->comment('专家用户ID（冗余字段）');
            $table->bigInteger('follower_user_id')->unsigned()->comment('跟单者用户ID');
            $table->bigInteger('copy_order_id')->unsigned()->comment('跟单记录ID');
            $table->bigInteger('expert_order_id')->unsigned()->comment('专家订单ID（关联trade_spot_order）');
            $table->bigInteger('follower_order_id')->unsigned()->comment('跟单者订单ID（关联trade_spot_order）');
            $table->decimal('profit', 20, 8)->comment('盈亏金额');
            $table->decimal('profit_sharing', 20, 8)->comment('分润金额');
            $table->decimal('profit_sharing_rate', 8, 4)->comment('分润比例 %');
            $table->bigInteger('currency_id')->unsigned()->comment('分润币种ID（默认USDT）');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->useCurrent()->useCurrentOnUpdate()->comment('更新时间');

            // 索引
            $table->index('expert_id', 'idx_expert');
            $table->index('expert_user_id', 'idx_expert_user_id');
            $table->index('follower_user_id', 'idx_follower_user_id');
            $table->index('copy_order_id', 'idx_copy_order_id');
            $table->index('expert_order_id', 'idx_expert_order_id');
            $table->index('follower_order_id', 'idx_follower_order_id');

            $table->comment('现货分润记录表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_spot_profit_sharing');
    }
};
