<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_contract_advanced_setting', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('follower_user_id')->unsigned()->comment('跟单者用户ID');
            $table->bigInteger('expert_id')->unsigned()->comment('专家ID');
            $table->bigInteger('expert_user_id')->unsigned()->comment('专家用户ID（冗余字段）');
            $table->bigInteger('currency_id')->unsigned()->comment('币种ID');
            $table->tinyInteger('copy_type')->comment('跟单方式：1-固定额度，2-倍率');
            $table->decimal('fixed_amount', 20, 8)->nullable()->comment('固定额度（USDT）');
            $table->decimal('rate', 8, 4)->nullable()->comment('倍率 %');
            $table->decimal('stop_loss_rate', 8, 4)->nullable()->comment('止损比例 %');
            $table->decimal('take_profit_rate', 8, 4)->nullable()->comment('止盈比例 %');
            $table->decimal('max_follow_amount', 20, 8)->nullable()->comment('最大跟随金额');
            $table->decimal('slippage_rate', 8, 4)->nullable()->comment('滑点比例 %');
            $table->tinyInteger('margin_mode')->default(1)->comment('保证金模式：1-跟随专家，2-全仓，3-逐仓');
            $table->tinyInteger('leverage_mode')->default(1)->comment('杠杆设置：1-跟随专家，2-指定杠杆');
            $table->integer('custom_leverage')->nullable()->comment('自定义杠杆倍数（仅当leverage_mode=2时有效）');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->useCurrent()->useCurrentOnUpdate()->comment('更新时间');

            // 索引
            $table->unique(['follower_user_id', 'expert_id', 'currency_id'], 'uk_follower_expert_currency');
            $table->index('expert_id', 'idx_expert');
            $table->index('expert_user_id', 'idx_expert_user_id');
            $table->index('currency_id', 'idx_currency_id');

            $table->comment('合约多元探索跟单高级设置表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_contract_advanced_setting');
    }
};
