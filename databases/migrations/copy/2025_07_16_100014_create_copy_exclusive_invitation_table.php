<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_exclusive_invitation', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('expert_id')->unsigned()->comment('专家ID');
            $table->string('expert_type', 255)->comment('专家模型类名（多态关联）');
            $table->bigInteger('expert_user_id')->unsigned()->comment('专家用户ID（冗余字段）');
            $table->tinyInteger('type')->comment('专家类型：1-合约，2-现货');
            $table->string('title', 100)->nullable()->comment('链接标题');
            $table->string('invite_code', 32)->comment('邀请码');
            $table->integer('max_count')->nullable()->comment('最大邀请人数');
            $table->integer('current_count')->default(0)->comment('当前邀请人数');
            $table->decimal('profit_sharing_rate', 8, 4)->nullable()->comment('分润比例 %');
            $table->timestamp('expired_at')->nullable()->comment('过期时间');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->useCurrent()->useCurrentOnUpdate()->comment('更新时间');

            // 索引
            $table->unique('invite_code', 'uk_invite_code');
            $table->index(['expert_id', 'expert_type'], 'idx_expert');
            $table->index('expert_user_id', 'idx_expert_user_id');

            $table->comment('尊享模式邀请表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_exclusive_invitation');
    }
};
