<?php

namespace App\Trait\Config;

use App\Enum\CurrencyConfigKey;
use App\Enum\MarketData\TickerSyncKey;
use App\Enum\MarketType;
use App\Model\Currency\Currency;

/**
 * 币种配置获取公共方法
 */
trait CurrencyConfigTrait
{

    protected function getRedis($redis = null,$poolName = 'default' )
    {
        if($redis != null){
            return $redis;
        }
        return $this->redis ?? redis($poolName);
    }

    /**
     * 获取币种的全部配置数据
     * @param int $currency_id
     * @return array|bool|\Redis
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function getCurrencyConfig(int $currency_id,$redis = null): array|bool|\Redis
    {
        try {
            return $this->getRedis($redis)->hGetAll(CurrencyConfigKey::getCurrencyKey($currency_id));
        }catch (\RedisException){
            return Currency::find($currency_id)->toArray();
        }
    }

    /**
     * 获取币种指定字段的配置数据
     * @param int $currency_id
     * @param string $key
     * @return mixed|null
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function getCurrencyConfigByKey(int $currency_id,string $key,$redis = null): mixed
    {
        try {
            return $this->getRedis($redis)->hGet(CurrencyConfigKey::getCurrencyKey($currency_id),$key) ?? null;
        }catch (\RedisException){
            return Currency::find($currency_id)->value($key);
        }
    }

    /**
     * 获取指定币种市场类型的 最新价格
     * @param int $currency_id
     * @param MarketType $marketType
     * @param $redis
     * @return int|void
     */
    protected function getCurrencyPrice(int $currency_id,MarketType $marketType,$redis = null)
    {
        try {
            return floatval($this->getRedis($redis,'market')->hGet(TickerSyncKey::getOuterTradeKey($currency_id,$marketType->value),'price') ?? 0);
        }catch (\Throwable){
            return 0.0;
        }
    }
}