<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 合约多元探索跟单高级设置模型
 */

namespace App\Model\Copy;

use App\Model\Copy\Enums\CopyType;
use App\Model\Copy\Enums\LeverageMode;
use App\Model\Copy\Enums\MarginMode;
use App\Model\User\User;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;

/**
 * @property int $id 主键ID
 * @property int $follower_user_id 跟单者用户ID
 * @property int $expert_id 专家ID
 * @property int $expert_user_id 专家用户ID（冗余字段）
 * @property int $currency_id 币种ID
 * @property CopyType $copy_type 跟单方式：1-固定额度，2-倍率
 * @property string|null $fixed_amount 固定额度（USDT）
 * @property string|null $rate 倍率 %
 * @property string|null $stop_loss_rate 止损比例 %
 * @property string|null $take_profit_rate 止盈比例 %
 * @property string|null $max_follow_amount 最大跟随金额
 * @property string|null $slippage_rate 滑点比例 %
 * @property MarginMode $margin_mode 保证金模式：1-跟随专家，2-全仓，3-逐仓
 * @property LeverageMode $leverage_mode 杠杆设置：1-跟随专家，2-指定杠杆
 * @property int|null $custom_leverage 自定义杠杆倍数（仅当leverage_mode=2时有效）
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 * @property User $followerUser 跟单者用户关联
 * @property CopyContractExpert $expert 专家关联
 */
final class CopyContractAdvancedSetting extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 跟单者用户ID
     */
    public const FIELD_FOLLOWER_USER_ID = 'follower_user_id';
    /**
     * 专家ID
     */
    public const FIELD_EXPERT_ID = 'expert_id';
    /**
     * 专家用户ID（冗余字段）
     */
    public const FIELD_EXPERT_USER_ID = 'expert_user_id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 跟单方式：1-固定额度，2-倍率
     */
    public const FIELD_COPY_TYPE = 'copy_type';
    /**
     * 固定额度（USDT）
     */
    public const FIELD_FIXED_AMOUNT = 'fixed_amount';
    /**
     * 倍率 %
     */
    public const FIELD_RATE = 'rate';
    /**
     * 止损比例 %
     */
    public const FIELD_STOP_LOSS_RATE = 'stop_loss_rate';
    /**
     * 止盈比例 %
     */
    public const FIELD_TAKE_PROFIT_RATE = 'take_profit_rate';
    /**
     * 最大跟随金额
     */
    public const FIELD_MAX_FOLLOW_AMOUNT = 'max_follow_amount';
    /**
     * 滑点比例 %
     */
    public const FIELD_SLIPPAGE_RATE = 'slippage_rate';
    /**
     * 保证金模式：1-跟随专家，2-全仓，3-逐仓
     */
    public const FIELD_MARGIN_MODE = 'margin_mode';
    /**
     * 杠杆设置：1-跟随专家，2-指定杠杆
     */
    public const FIELD_LEVERAGE_MODE = 'leverage_mode';
    /**
     * 自定义杠杆倍数（仅当leverage_mode=2时有效）
     */
    public const FIELD_CUSTOM_LEVERAGE = 'custom_leverage';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_contract_advanced_setting';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'follower_user_id', // 跟单者用户ID
        'expert_id', // 专家ID
        'expert_user_id', // 专家用户ID（冗余字段）
        'currency_id', // 币种ID
        'copy_type', // 跟单方式：1-固定额度，2-倍率
        'fixed_amount', // 固定额度（USDT）
        'rate', // 倍率 %
        'stop_loss_rate', // 止损比例 %
        'take_profit_rate', // 止盈比例 %
        'max_follow_amount', // 最大跟随金额
        'slippage_rate', // 滑点比例 %
        'margin_mode', // 保证金模式：1-跟随专家，2-全仓，3-逐仓
        'leverage_mode', // 杠杆设置：1-跟随专家，2-指定杠杆
        'custom_leverage', // 自定义杠杆倍数（仅当leverage_mode=2时有效）
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'follower_user_id' => 'integer', // 跟单者用户ID
        'expert_id' => 'integer', // 专家ID
        'expert_user_id' => 'integer', // 专家用户ID（冗余字段）
        'currency_id' => 'integer', // 币种ID
        'copy_type' => CopyType::class, // 跟单方式：1-固定额度，2-倍率
        'fixed_amount' => 'decimal:8', // 固定额度（USDT）
        'rate' => 'decimal:4', // 倍率 %
        'stop_loss_rate' => 'decimal:4', // 止损比例 %
        'take_profit_rate' => 'decimal:4', // 止盈比例 %
        'max_follow_amount' => 'decimal:8', // 最大跟随金额
        'slippage_rate' => 'decimal:4', // 滑点比例 %
        'margin_mode' => MarginMode::class, // 保证金模式：1-跟随专家，2-全仓，3-逐仓
        'leverage_mode' => LeverageMode::class, // 杠杆设置：1-跟随专家，2-指定杠杆
        'custom_leverage' => 'integer', // 自定义杠杆倍数（仅当leverage_mode=2时有效）
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 跟单者用户关联
     */
    public function followerUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'follower_user_id', 'id');
    }

    /**
     * 专家关联
     */
    public function expert(): BelongsTo
    {
        return $this->belongsTo(CopyContractExpert::class, 'expert_id', 'id');
    }

    /**
     * 专家用户关联
     */
    public function expertUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'expert_user_id', 'id');
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取跟单者用户ID
     */
    public function getFollowerUserId(): int
    {
        return $this->follower_user_id;
    }

    /**
     * 设置跟单者用户ID
     */
    public function setFollowerUserId(int $value): static
    {
        $this->follower_user_id = $value;
        return $this;
    }

    /**
     * 获取专家ID
     */
    public function getExpertId(): int
    {
        return $this->expert_id;
    }

    /**
     * 设置专家ID
     */
    public function setExpertId(int $value): static
    {
        $this->expert_id = $value;
        return $this;
    }

    /**
     * 获取专家用户ID（冗余字段）
     */
    public function getExpertUserId(): int
    {
        return $this->expert_user_id;
    }

    /**
     * 设置专家用户ID（冗余字段）
     */
    public function setExpertUserId(int $value): static
    {
        $this->expert_user_id = $value;
        return $this;
    }

    /**
     * 获取币种ID
     */
    public function getCurrencyId(): int
    {
        return $this->currency_id;
    }

    /**
     * 设置币种ID
     */
    public function setCurrencyId(int $value): static
    {
        $this->currency_id = $value;
        return $this;
    }

    /**
     * 获取跟单方式
     */
    public function getCopyType(): CopyType
    {
        return $this->copy_type;
    }

    /**
     * 设置跟单方式
     */
    public function setCopyType(CopyType $value): static
    {
        $this->copy_type = $value;
        return $this;
    }

    /**
     * 获取固定额度（USDT）
     */
    public function getFixedAmount(): ?string
    {
        return $this->fixed_amount;
    }

    /**
     * 设置固定额度（USDT）
     */
    public function setFixedAmount(?string $value): static
    {
        $this->fixed_amount = $value;
        return $this;
    }

    /**
     * 获取倍率 %
     */
    public function getRate(): ?string
    {
        return $this->rate;
    }

    /**
     * 设置倍率 %
     */
    public function setRate(?string $value): static
    {
        $this->rate = $value;
        return $this;
    }

    /**
     * 获取止损比例 %
     */
    public function getStopLossRate(): ?string
    {
        return $this->stop_loss_rate;
    }

    /**
     * 设置止损比例 %
     */
    public function setStopLossRate(?string $value): static
    {
        $this->stop_loss_rate = $value;
        return $this;
    }

    /**
     * 获取止盈比例 %
     */
    public function getTakeProfitRate(): ?string
    {
        return $this->take_profit_rate;
    }

    /**
     * 设置止盈比例 %
     */
    public function setTakeProfitRate(?string $value): static
    {
        $this->take_profit_rate = $value;
        return $this;
    }

    /**
     * 获取最大跟随金额
     */
    public function getMaxFollowAmount(): ?string
    {
        return $this->max_follow_amount;
    }

    /**
     * 设置最大跟随金额
     */
    public function setMaxFollowAmount(?string $value): static
    {
        $this->max_follow_amount = $value;
        return $this;
    }

    /**
     * 获取滑点比例 %
     */
    public function getSlippageRate(): ?string
    {
        return $this->slippage_rate;
    }

    /**
     * 设置滑点比例 %
     */
    public function setSlippageRate(?string $value): static
    {
        $this->slippage_rate = $value;
        return $this;
    }

    /**
     * 获取保证金模式
     */
    public function getMarginMode(): MarginMode
    {
        return $this->margin_mode;
    }

    /**
     * 设置保证金模式
     */
    public function setMarginMode(MarginMode $value): static
    {
        $this->margin_mode = $value;
        return $this;
    }

    /**
     * 获取杠杆设置
     */
    public function getLeverageMode(): LeverageMode
    {
        return $this->leverage_mode;
    }

    /**
     * 设置杠杆设置
     */
    public function setLeverageMode(LeverageMode $value): static
    {
        $this->leverage_mode = $value;
        return $this;
    }

    /**
     * 获取自定义杠杆倍数
     */
    public function getCustomLeverage(): ?int
    {
        return $this->custom_leverage;
    }

    /**
     * 设置自定义杠杆倍数
     */
    public function setCustomLeverage(?int $value): static
    {
        $this->custom_leverage = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }
}
