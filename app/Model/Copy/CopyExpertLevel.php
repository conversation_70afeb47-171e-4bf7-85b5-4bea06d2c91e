<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 交易专家等级模型
 */

namespace App\Model\Copy;

use App\Model\Copy\Enums\ExpertType;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\HasMany;

/**
 * @property int $id 主键ID
 * @property ExpertType $type 类型：1-合约，2-现货
 * @property int $level 等级
 * @property string $name 等级名称
 * @property string|null $icon 等级图标
 * @property string $condition_amount 条件一：带单金额（USDT）
 * @property string|null $condition_follow_amount 条件二：跟单者总跟单资金（USDT）
 * @property int|null $condition_follow_count 条件二：跟单交易人数（人）
 * @property int $max_follow_count 最大可带单人数
 * @property string $max_profit_rate 最大分润比例 %
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
final class CopyExpertLevel extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 类型：1-合约，2-现货
     */
    public const FIELD_TYPE = 'type';
    /**
     * 等级
     */
    public const FIELD_LEVEL = 'level';
    /**
     * 等级名称
     */
    public const FIELD_NAME = 'name';
    /**
     * 等级图标
     */
    public const FIELD_ICON = 'icon';
    /**
     * 条件一：带单金额（USDT）
     */
    public const FIELD_CONDITION_AMOUNT = 'condition_amount';
    /**
     * 条件二：跟单者总跟单资金（USDT）
     */
    public const FIELD_CONDITION_FOLLOW_AMOUNT = 'condition_follow_amount';
    /**
     * 条件二：跟单交易人数（人）
     */
    public const FIELD_CONDITION_FOLLOW_COUNT = 'condition_follow_count';
    /**
     * 最大可带单人数
     */
    public const FIELD_MAX_FOLLOW_COUNT = 'max_follow_count';
    /**
     * 最大分润比例 %
     */
    public const FIELD_MAX_PROFIT_RATE = 'max_profit_rate';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_expert_level';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'type', // 类型：1-合约，2-现货
        'level', // 等级
        'name', // 等级名称
        'icon', // 等级图标
        'condition_amount', // 条件一：带单金额（USDT）
        'condition_follow_amount', // 条件二：跟单者总跟单资金（USDT）
        'condition_follow_count', // 条件二：跟单交易人数（人）
        'max_follow_count', // 最大可带单人数
        'max_profit_rate', // 最大分润比例 %
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'type' => ExpertType::class, // 类型：1-合约，2-现货
        'level' => 'integer', // 等级
        'condition_amount' => 'decimal:8', // 条件一：带单金额（USDT）
        'condition_follow_amount' => 'decimal:8', // 条件二：跟单者总跟单资金（USDT）
        'condition_follow_count' => 'integer', // 条件二：跟单交易人数（人）
        'max_follow_count' => 'integer', // 最大可带单人数
        'max_profit_rate' => 'decimal:4', // 最大分润比例 %
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 合约专家关联
     */
    public function contractExperts(): HasMany
    {
        return $this->hasMany(CopyContractExpert::class, 'level_id', 'id');
    }

    /**
     * 现货专家关联
     */
    public function spotExperts(): HasMany
    {
        return $this->hasMany(CopySpotExpert::class, 'level_id', 'id');
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取类型
     */
    public function getType(): ExpertType
    {
        return $this->type;
    }

    /**
     * 设置类型
     */
    public function setType(ExpertType $value): static
    {
        $this->type = $value;
        return $this;
    }

    /**
     * 获取等级
     */
    public function getLevel(): int
    {
        return $this->level;
    }

    /**
     * 设置等级
     */
    public function setLevel(int $value): static
    {
        $this->level = $value;
        return $this;
    }

    /**
     * 获取等级名称
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * 设置等级名称
     */
    public function setName(string $value): static
    {
        $this->name = $value;
        return $this;
    }

    /**
     * 获取等级图标
     */
    public function getIcon(): ?string
    {
        return $this->icon;
    }

    /**
     * 设置等级图标
     */
    public function setIcon(?string $value): static
    {
        $this->icon = $value;
        return $this;
    }

    /**
     * 获取条件一：带单金额（USDT）
     */
    public function getConditionAmount(): string
    {
        return $this->condition_amount;
    }

    /**
     * 设置条件一：带单金额（USDT）
     */
    public function setConditionAmount(string $value): static
    {
        $this->condition_amount = $value;
        return $this;
    }

    /**
     * 获取条件二：跟单者总跟单资金（USDT）
     */
    public function getConditionFollowAmount(): ?string
    {
        return $this->condition_follow_amount;
    }

    /**
     * 设置条件二：跟单者总跟单资金（USDT）
     */
    public function setConditionFollowAmount(?string $value): static
    {
        $this->condition_follow_amount = $value;
        return $this;
    }

    /**
     * 获取条件二：跟单交易人数（人）
     */
    public function getConditionFollowCount(): ?int
    {
        return $this->condition_follow_count;
    }

    /**
     * 设置条件二：跟单交易人数（人）
     */
    public function setConditionFollowCount(?int $value): static
    {
        $this->condition_follow_count = $value;
        return $this;
    }

    /**
     * 获取最大可带单人数
     */
    public function getMaxFollowCount(): int
    {
        return $this->max_follow_count;
    }

    /**
     * 设置最大可带单人数
     */
    public function setMaxFollowCount(int $value): static
    {
        $this->max_follow_count = $value;
        return $this;
    }

    /**
     * 获取最大分润比例 %
     */
    public function getMaxProfitRate(): string
    {
        return $this->max_profit_rate;
    }

    /**
     * 设置最大分润比例 %
     */
    public function setMaxProfitRate(string $value): static
    {
        $this->max_profit_rate = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }
}
