<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 合约跟单记录模型
 */

namespace App\Model\Copy;

use App\Model\Copy\Enums\CopyMode;
use App\Model\User\User;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;

/**
 * @property int $id 主键ID
 * @property int $follower_user_id 跟单者用户ID
 * @property int $expert_id 专家ID
 * @property int $expert_user_id 专家用户ID（冗余字段）
 * @property CopyMode $mode 跟单模式：1-智能比例，2-多元探索
 * @property int $expert_order_id 专家订单ID（关联trade_perpetual_order）
 * @property int $expert_position_id 专家仓位ID（关联trade_perpetual_position）
 * @property int $follower_order_id 跟单者订单ID（关联trade_perpetual_order）
 * @property int $follower_position_id 跟单者仓位ID（关联trade_perpetual_position）
 * @property string $profit_sharing_rate 分润比例 %
 * @property bool $is_exclusive 是否尊享模式
 * @property array $copy_settings_snapshot 跟单配置参数快照
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 * @property User $followerUser 跟单者用户关联
 * @property CopyContractExpert $expert 专家关联
 */
final class CopyContractOrder extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 跟单者用户ID
     */
    public const FIELD_FOLLOWER_USER_ID = 'follower_user_id';
    /**
     * 专家ID
     */
    public const FIELD_EXPERT_ID = 'expert_id';
    /**
     * 专家用户ID（冗余字段）
     */
    public const FIELD_EXPERT_USER_ID = 'expert_user_id';
    /**
     * 跟单模式：1-智能比例，2-多元探索
     */
    public const FIELD_MODE = 'mode';
    /**
     * 专家订单ID（关联trade_perpetual_order）
     */
    public const FIELD_EXPERT_ORDER_ID = 'expert_order_id';
    /**
     * 专家仓位ID（关联trade_perpetual_position）
     */
    public const FIELD_EXPERT_POSITION_ID = 'expert_position_id';
    /**
     * 跟单者订单ID（关联trade_perpetual_order）
     */
    public const FIELD_FOLLOWER_ORDER_ID = 'follower_order_id';
    /**
     * 跟单者仓位ID（关联trade_perpetual_position）
     */
    public const FIELD_FOLLOWER_POSITION_ID = 'follower_position_id';
    /**
     * 分润比例 %
     */
    public const FIELD_PROFIT_SHARING_RATE = 'profit_sharing_rate';
    /**
     * 是否尊享模式
     */
    public const FIELD_IS_EXCLUSIVE = 'is_exclusive';
    /**
     * 跟单配置参数快照
     */
    public const FIELD_COPY_SETTINGS_SNAPSHOT = 'copy_settings_snapshot';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_contract_order';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'follower_user_id', // 跟单者用户ID
        'expert_id', // 专家ID
        'expert_user_id', // 专家用户ID（冗余字段）
        'mode', // 跟单模式：1-智能比例，2-多元探索
        'expert_order_id', // 专家订单ID（关联trade_perpetual_order）
        'expert_position_id', // 专家仓位ID（关联trade_perpetual_position）
        'follower_order_id', // 跟单者订单ID（关联trade_perpetual_order）
        'follower_position_id', // 跟单者仓位ID（关联trade_perpetual_position）
        'profit_sharing_rate', // 分润比例 %
        'is_exclusive', // 是否尊享模式
        'copy_settings_snapshot', // 跟单配置参数快照
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'follower_user_id' => 'integer', // 跟单者用户ID
        'expert_id' => 'integer', // 专家ID
        'expert_user_id' => 'integer', // 专家用户ID（冗余字段）
        'mode' => CopyMode::class, // 跟单模式：1-智能比例，2-多元探索
        'expert_order_id' => 'integer', // 专家订单ID（关联trade_perpetual_order）
        'expert_position_id' => 'integer', // 专家仓位ID（关联trade_perpetual_position）
        'follower_order_id' => 'integer', // 跟单者订单ID（关联trade_perpetual_order）
        'follower_position_id' => 'integer', // 跟单者仓位ID（关联trade_perpetual_position）
        'profit_sharing_rate' => 'decimal:4', // 分润比例 %
        'is_exclusive' => 'boolean', // 是否尊享模式
        'copy_settings_snapshot' => 'array', // 跟单配置参数快照
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 跟单者用户关联
     */
    public function followerUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'follower_user_id', 'id');
    }

    /**
     * 专家关联
     */
    public function expert(): BelongsTo
    {
        return $this->belongsTo(CopyContractExpert::class, 'expert_id', 'id');
    }

    /**
     * 专家用户关联
     */
    public function expertUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'expert_user_id', 'id');
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取跟单者用户ID
     */
    public function getFollowerUserId(): int
    {
        return $this->follower_user_id;
    }

    /**
     * 设置跟单者用户ID
     */
    public function setFollowerUserId(int $value): static
    {
        $this->follower_user_id = $value;
        return $this;
    }

    /**
     * 获取专家ID
     */
    public function getExpertId(): int
    {
        return $this->expert_id;
    }

    /**
     * 设置专家ID
     */
    public function setExpertId(int $value): static
    {
        $this->expert_id = $value;
        return $this;
    }

    /**
     * 获取专家用户ID（冗余字段）
     */
    public function getExpertUserId(): int
    {
        return $this->expert_user_id;
    }

    /**
     * 设置专家用户ID（冗余字段）
     */
    public function setExpertUserId(int $value): static
    {
        $this->expert_user_id = $value;
        return $this;
    }

    /**
     * 获取跟单模式
     */
    public function getMode(): CopyMode
    {
        return $this->mode;
    }

    /**
     * 设置跟单模式
     */
    public function setMode(CopyMode $value): static
    {
        $this->mode = $value;
        return $this;
    }

    /**
     * 获取专家订单ID（关联trade_perpetual_order）
     */
    public function getExpertOrderId(): int
    {
        return $this->expert_order_id;
    }

    /**
     * 设置专家订单ID（关联trade_perpetual_order）
     */
    public function setExpertOrderId(int $value): static
    {
        $this->expert_order_id = $value;
        return $this;
    }

    /**
     * 获取专家仓位ID（关联trade_perpetual_position）
     */
    public function getExpertPositionId(): int
    {
        return $this->expert_position_id;
    }

    /**
     * 设置专家仓位ID（关联trade_perpetual_position）
     */
    public function setExpertPositionId(int $value): static
    {
        $this->expert_position_id = $value;
        return $this;
    }

    /**
     * 获取跟单者订单ID（关联trade_perpetual_order）
     */
    public function getFollowerOrderId(): int
    {
        return $this->follower_order_id;
    }

    /**
     * 设置跟单者订单ID（关联trade_perpetual_order）
     */
    public function setFollowerOrderId(int $value): static
    {
        $this->follower_order_id = $value;
        return $this;
    }

    /**
     * 获取跟单者仓位ID（关联trade_perpetual_position）
     */
    public function getFollowerPositionId(): int
    {
        return $this->follower_position_id;
    }

    /**
     * 设置跟单者仓位ID（关联trade_perpetual_position）
     */
    public function setFollowerPositionId(int $value): static
    {
        $this->follower_position_id = $value;
        return $this;
    }

    /**
     * 获取分润比例 %
     */
    public function getProfitSharingRate(): string
    {
        return $this->profit_sharing_rate;
    }

    /**
     * 设置分润比例 %
     */
    public function setProfitSharingRate(string $value): static
    {
        $this->profit_sharing_rate = $value;
        return $this;
    }

    /**
     * 获取是否尊享模式
     */
    public function getIsExclusive(): bool
    {
        return $this->is_exclusive;
    }

    /**
     * 设置是否尊享模式
     */
    public function setIsExclusive(bool $value): static
    {
        $this->is_exclusive = $value;
        return $this;
    }

    /**
     * 获取跟单配置参数快照
     */
    public function getCopySettingsSnapshot(): array
    {
        return $this->copy_settings_snapshot;
    }

    /**
     * 设置跟单配置参数快照
     */
    public function setCopySettingsSnapshot(array $value): static
    {
        $this->copy_settings_snapshot = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }
}
