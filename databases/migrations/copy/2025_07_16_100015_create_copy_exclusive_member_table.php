<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_exclusive_member', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('invitation_id')->unsigned()->comment('邀请记录ID');
            $table->bigInteger('expert_id')->unsigned()->comment('专家ID');
            $table->string('expert_type', 255)->comment('专家模型类名（多态关联）');
            $table->bigInteger('expert_user_id')->unsigned()->comment('专家用户ID（冗余字段）');
            $table->tinyInteger('type')->comment('专家类型：1-合约，2-现货');
            $table->bigInteger('follower_user_id')->unsigned()->comment('跟单者用户ID');
            $table->decimal('profit_sharing_rate', 8, 4)->comment('分润比例（可单独修改）%');
            $table->timestamp('joined_at')->useCurrent()->comment('加入时间');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->useCurrent()->useCurrentOnUpdate()->comment('更新时间');

            // 索引
            $table->unique(['invitation_id', 'follower_user_id'], 'uk_invitation_follower');
            $table->index(['expert_id', 'expert_type'], 'idx_expert');
            $table->index('expert_user_id', 'idx_expert_user_id');
            $table->index('follower_user_id', 'idx_follower_user_id');

            $table->comment('尊享模式成员表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_exclusive_member');
    }
};
